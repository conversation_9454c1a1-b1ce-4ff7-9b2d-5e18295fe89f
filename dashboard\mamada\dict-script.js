// dict-script.js
document.addEventListener('DOMContentLoaded', async () => {
    // --- URLs de Datos ---
    const JMDICT_URL = '../data/jmdict.json';
    const KANJIDIC2_URL = '../data/kanjidic.json';
    const KRADFILE_URL = '../data/kradfile-3.6.1.json';
    const KANJI_JSON_URL = '../data/kanji.json'; // Para JLPT actualizado de Kanji
    const TATOEBA_ES_URL = '../data/tatoeba-esp.tsv';
    const JLPT_URL = '../data/jlpt.json';

    // --- Element<PERSON> del DOM Principales ---
    const searchInput = document.getElementById('dictionary-search-input');
    const searchButton = document.getElementById('dictionary-search-button');
    const vocabResultsList = document.getElementById('vocabulary-results-list');
    const searchSuggestionContainer = document.getElementById('search-suggestion-container');
    const searchOriginalQueryLink = document.getElementById('search-original-query-link');
    const kanjiResultsList = document.getElementById('kanji-results-list');
    const vocabResultsTitle = document.getElementById('vocabulary-results-title');
    const kanjiResultsTitle = document.getElementById('kanji-results-title');
    const dictionaryInitialMessage = document.getElementById('dictionary-initial-message');
    const searchResultsContainer = document.querySelector('.search-results-container');
    const searchResultsTabsContainer = document.getElementById('search-results-tabs-container');
    const searchResultsTabButtons = document.querySelectorAll('.search-results-tab-button');

    const vocabShowMoreButton = document.getElementById('vocab-show-more-button');
    const kanjiShowMoreButton = document.getElementById('kanji-show-more-button');


    const mainView = document.getElementById('dictionary-main-view');
    const vocabDetailView = document.getElementById('dictionary-vocab-detail-view');
    const kanjiDetailView = document.getElementById('dictionary-kanji-detail-view');
    // const collectionsView = document.getElementById('dictionary-collections-view'); // REMOVED

    // Elementos de Detalle de Vocabulario
    const vocabDetailBackButton = document.getElementById('vocab-detail-back-button');
    const vocabDetailTermDisplayWrapper = vocabDetailView.querySelector('.vocab-detail-term-display-wrapper');
    const vocabDetailTermDisplay = vocabDetailView.querySelector('.vocab-detail-term-display');
    const vocabDetailTagsContainer = document.getElementById('vocab-detail-tags-container');
    const vocabDetailSensesContainer = document.getElementById('vocab-detail-senses-container');
    const vocabDetailSensesTitle = document.getElementById('vocab-detail-senses-title');
    const vocabDetailSensesList = document.getElementById('vocab-detail-senses-list');
    const vocabDetailEnglishSensesContainer = document.getElementById('vocab-detail-english-senses-container');
    const vocabDetailEnglishSensesList = document.getElementById('vocab-detail-english-senses-list');
    const vocabDetailKanjiGrid = document.getElementById('vocab-detail-kanji-grid');
    const vocabDetailExamplesList = document.getElementById('vocab-detail-examples-list');
    const vocabDetailExamplesTitle = document.getElementById('vocab-detail-examples-title');
    const vocabExamplesShowMoreButton = document.getElementById('vocab-examples-show-more-button');
    const vocabDetailCopyButton = document.getElementById('vocab-detail-copy-button');
    const vocabDetailLikeButton = document.getElementById('vocab-detail-like-button');
    const vocabDetailAddCollectionButton = document.getElementById('vocab-detail-add-collection-button');

    // Elementos de Detalle de Kanji
    const kanjiDetailBackButton = document.getElementById('kanji-detail-back-button-dict');
    const kanjiDetailMainMeaningHeader = document.getElementById('kanji-detail-main-meaning-header-dict');
    const kanjiDetailAnimationTarget = document.getElementById('kanji-detail-animation-target-dict');
    const kanjiDetailMainInfo = document.getElementById('kanji-detail-main-info-dict');
    const kanjiDetailCharForData = kanjiDetailMainInfo.querySelector('.kanji-char-for-data-dict');
    const kanjiDetailJlpt = document.getElementById('kanji-detail-jlpt-dict');
    const kanjiDetailFrequency = document.getElementById('kanji-detail-frequency-dict');
    const kanjiDetailStrokes = document.getElementById('kanji-detail-strokes-dict');
    const kanjiDetailOnYomi = document.getElementById('kanji-detail-on-yomi-dict');
    const kanjiDetailKunYomi = document.getElementById('kanji-detail-kun-yomi-dict');
    const kanjiDetailCopyButtonDict = document.getElementById('kanji-detail-copy-button-dict');
    const kanjiDetailLikeButtonDict = document.getElementById('kanji-detail-like-button-dict');
    const kanjiDetailAddCollectionButtonDict = document.getElementById('kanji-detail-add-collection-button-dict');
    const kanjiMeaningsTab = document.getElementById('meanings-dict');
    const kanjiRadicalsTab = document.getElementById('radicals-dict');
    const kanjiUserNotesTextarea = document.getElementById('kanji-detail-user-notes-dict');
    const kanjiEditNotesButton = document.getElementById('kanji-detail-edit-notes-dict');
    const kanjiSaveNotesButton = document.getElementById('kanji-detail-save-notes-dict');
    const kanjiResetNotesButton = document.getElementById('kanji-detail-reset-notes-dict'); // kanjivaleordsList
    const kanjiCommonWordsList = document.getElementById('kanji-detail-common-words-list-dict');
    const kanjiAllWordsList = document.getElementById('kanji-detail-all-words-list-dict');
    const kanjiCommonWordsShowMoreButton = document.getElementById('kanji-common-words-show-more-button');
    const kanjiAllWordsShowMoreButton = document.getElementById('kanji-all-words-show-more-button');

    // Elementos de Colecciones - REMOVED
    // const dictionaryCollectionsButton = document.getElementById('dictionary-collections-button');
    // const collectionsBackToMainButton = document.getElementById('collections-back-to-main-dictionary-button');
    // const collectionsListContainer = document.getElementById('dictionary-collections-list-container');
    // const createNewCollectionButtonDict = document.getElementById('collections-create-new-button-dict');
    // const collectionTabButtons = document.querySelectorAll('.collection-tab-button-dict');

    // Modals
    const addToCollectionModal = document.getElementById('dict-add-to-collection-modal'); // Kept for "Favoritos" and creating new on-the-fly
    const addToCollectionModalClose = addToCollectionModal.querySelector('.modal-fusoku-close-button');
    const existingCollectionsListModal = document.getElementById('dict-existing-collections-list-modal');
    const newCollectionNameModalInput = document.getElementById('dict-new-collection-name-modal');
    const createAndAddToNewCollectionBtnModal = document.getElementById('dict-create-and-add-to-new-collection-button-modal');
    const addToCollectionFeedback = document.getElementById('dict-add-to-collection-feedback');
    const modalCollectionTitle = document.getElementById('dict-modal-collection-title');

    // const createCollectionModal = document.getElementById('dict-create-collection-modal'); // REMOVED
    // const createCollectionModalClose = createCollectionModal.querySelector('.modal-fusoku-close-button'); // REMOVED
    // const newCollectionNameMainModalInput = document.getElementById('dict-new-collection-name-main-modal'); // REMOVED
    // const confirmCreateCollectionBtnModal = document.getElementById('dict-confirm-create-collection-button-modal'); // REMOVED
    // const createCollectionFeedback = document.getElementById('dict-create-collection-feedback'); // REMOVED


    // --- Estado de la Aplicación ---
    let jmdictData = [];
    let kanjidicData = {}; 
    let kradfileData = {};
    let kanjiJsonData = {}; 
    let tatoebaSentences = [];
    let jlptDataRaw = []; // Ensure it's initialized as an array
    let jlptLookupMap = new Map(); // Defined here for broader scope

    let userProgress = { 
        theme: 'light', 
        dictionaryCollections: { 
            vocab: [{ name: "Favoritos Vocab", items: [], isSystem: true }],
            kanji: [{ name: "Favoritos Kanji (Diccionario)", items: [], isSystem: true }]
        },
        kanjiUserNotes: {} 
    };

    let currentDetailTerm = null; 
    let currentDetailKanji = null; 
    let dmakInstanceDict = null;
    let dmakIsAnimatingOnClickDict = false;
    let navigationHistory = []; 
    let currentItemForModal = null; 
    let currentCollectionTypeForModal = 'vocab'; 

    const USUALLY_KANA_WRITING_TAG = " 通常は仮名書き ";
    const ITEMS_PER_LOAD = 20; 

    let currentVocabResults = [];
    let currentKanjiResults = [];
    let currentVocabOffset = 0;
    let currentKanjiOffset = 0;

    let currentExampleSentences = [];
    let currentExampleOffset = 0;

    let currentKanjiCommonWords = [];
    let currentKanjiCommonWordsOffset = 0;
    let currentKanjiAllWords = [];
    let currentKanjiAllWordsOffset = 0;

    // ========== PARSE URL HASH & HANDLE NAVIGATION ==========
    function parseLocationHashDictionary() {
        const hash = window.location.hash.substring(1); // Remove '#'
        if (!hash) return null;

        const params = new URLSearchParams(hash);
        const type = params.get('type');
        const id = params.get('id');

        if (type && id) {
            return { type: decodeURIComponent(type), id: decodeURIComponent(id) };
        }
        return null;
    }

    function handleDictionaryNavigation() {
        const navigationData = parseLocationHashDictionary();
        if (navigationData) {
            console.log('URL Hash Navigation: Attempting to show Dictionary detail for:', navigationData);
            navigationHistory = [{ view: 'main', id: null }]; // Initialize history

            if (navigationData.type === 'wordJmdict') {
                if (jmdictData && jmdictData.length > 0) {
                    showView('vocabDetail', navigationData.id);
                } else {
                    console.error('JMDict data not ready for vocabDetail navigation from hash.');
                    return false; // Data not ready
                }
            } else if (navigationData.type === 'kanjiKanjidic' || navigationData.type === 'kanjiFusoku') {
                if (kanjidicData && Object.keys(kanjidicData).length > 0) {
                    showView('kanjiDetail', navigationData.id);
                } else {
                    console.error('Kanjidic data not ready for kanjiDetail navigation from hash.');
                    return false; // Data not ready
                }
            } else {
                console.error('Unknown navigationData.type from hash:', navigationData.type);
                return false; // Unknown type
            }
            return true; // Navigation was handled
        }
        return false; // No valid hash for navigation
    }

    // ========== FUNCIONES DE PARSEO DE DATOS ==========
    function parseTatoebaTsv(tsvText) {
        const sentences = [];
        if (!tsvText) return sentences; // Manejar caso de texto vacío o nulo

        const lines = tsvText.split('\n');
        for (const line of lines) {
            if (line.trim() === '') continue; // Omitir líneas vacías
            const parts = line.split('\t');
            // Asumimos que la primera columna es japonés y la segunda español
            if (parts.length >= 2) {
                sentences.push({ jp: parts[0].trim(), es: parts[1].trim() });
            } else if (parts.length === 1 && parts[0].trim() !== '') {
                // Podría ser una línea malformada, opcionalmente registrarla o ignorarla
                console.warn('Línea malformada en Tatoeba TSV:', line);
            }
        }
        return sentences;
    }
    // ========== CARGA DE DATOS Y PREPROCESAMIENTO ==========
    async function loadAllDictionaryData() {
        try {
            // Load other data sources (keep these)
            const [kanjidicResponse, kradfileResponse, kanjiJsonResponse, tatoebaResponse, jlptResponse] = await Promise.all([
                fetch(KANJIDIC2_URL),
                fetch(KRADFILE_URL),
                fetch(KANJI_JSON_URL),
                fetch(TATOEBA_ES_URL),
                fetch(JLPT_URL)
            ]);

            // Process other data sources
            const kanjidicText = await kanjidicResponse.text();
            const kradfileText = await kradfileResponse.text();
            const kanjiJsonText = await kanjiJsonResponse.text();
            const tatoebaText = await tatoebaResponse.text();
            const jlptText = await jlptResponse.text();

            // Parse other data sources
            kanjidicData = JSON.parse(kanjidicText);
            kradfileData = JSON.parse(kradfileText);
            kanjiJsonData = JSON.parse(kanjiJsonText);
            tatoebaSentences = parseTatoebaTsv(tatoebaText);
            
            // Parse JLPT data
            const jlptData = JSON.parse(jlptText);
            jlptLookupMap = new Map();
            for (const entry of jlptData) {
                if (entry.k && entry.r && entry.l) {
                    const key = entry.k + "#" + entry.r;
                    jlptLookupMap.set(key, entry.l);
                    if (!jlptLookupMap.has(entry.k)) {
                        jlptLookupMap.set(entry.k, entry.l);
                    }
                    if (!jlptLookupMap.has(entry.r)) {
                        jlptLookupMap.set(entry.r, entry.l);
                    }
                }
            }

            // No need to load JMdict CSV anymore since we're using MySQL
            // Just initialize an empty array for compatibility
            jmdictData = [];

            if (document.body.classList.contains('dark-mode')) {
                userProgress.theme = 'dark';
            } else {
                userProgress.theme = 'light';
            }
            const themeObserver = new MutationObserver(mutations => {
                mutations.forEach(mutation => {
                    if (mutation.attributeName === 'class') {
                        userProgress.theme = document.body.classList.contains('dark-mode') ? 'dark' : 'light';
                        if (kanjiDetailView.classList.contains('active-dict-view') && currentDetailKanji) {
                            initDmakDict(currentDetailKanji.l); 
                        }
                    }
                });
            });
            themeObserver.observe(document.body, { attributes: true });


            loadUserProgressLocal();
            initComponents();
            if (searchResultsContainer) searchResultsContainer.style.display = 'none';
            if (dictionaryInitialMessage) dictionaryInitialMessage.style.display = 'block';

        } catch (error) {
            console.error("Error crítico al cargar datos del diccionario:", error);
            if (mainView) {
                 mainView.innerHTML = `<p class="error-message">Error al cargar los datos del diccionario: ${error.message}. Por favor, recarga la página o revisa la consola.</p>`;
            }
        }
    }

    function findJlptForTerm(jmdictEntry) {
        for (const kjElement of jmdictEntry.KJ) {
            const kanjiForm = kjElement.tx;
            if (!kanjiForm) continue;

            for (const knElement of jmdictEntry.KN) {
                const kanaReading = knElement.tx;
                if (!kanaReading) continue;

                const keyForKanjiCombo = kanjiForm + "#" + kanaReading;
                if (jlptLookupMap.has(keyForKanjiCombo)) {
                    return jlptLookupMap.get(keyForKanjiCombo);
                }
            }
        }
        for (const knElement of jmdictEntry.KN) {
            const kanaReading = knElement.tx;
            if (!kanaReading) continue;

            const primaryKanjiForm = jmdictEntry.KJ[0]?.tx;
            if (primaryKanjiForm && primaryKanjiForm === kanaReading) {
                if (jlptLookupMap.has(kanaReading)) {
                    return jlptLookupMap.get(kanaReading);
                }
            }
            if (jmdictEntry.KJ.length === 0 || jmdictEntry.KJ.every(k => !k.tx)) {
                if (jlptLookupMap.has(kanaReading)) {
                    return jlptLookupMap.get(kanaReading);
                }
            }
        }
        return null;
    }

    function loadUserProgressLocal() {
        const saved = localStorage.getItem('fusokuDictionaryProgress');
        if (saved) {
            const parsed = JSON.parse(saved);
            const currentTheme = userProgress.theme;
            userProgress = { ...userProgress, ...parsed };
            userProgress.theme = currentTheme; 

            if (!userProgress.dictionaryCollections.vocab.find(c => c.name === "Favoritos Vocab" && c.isSystem)) {
                userProgress.dictionaryCollections.vocab.unshift({ name: "Favoritos Vocab", items: [], isSystem: true });
            }
            if (!userProgress.dictionaryCollections.kanji.find(c => c.name === "Favoritos Kanji (Diccionario)" && c.isSystem)) {
                userProgress.dictionaryCollections.kanji.unshift({ name: "Favoritos Kanji (Diccionario)", items: [], isSystem: true });
            }
        } else {
             userProgress.dictionaryCollections = {
                vocab: [{ name: "Favoritos Vocab", items: [], isSystem: true }],
                kanji: [{ name: "Favoritos Kanji (Diccionario)", items: [], isSystem: true }]
            };
        }
        if (typeof userProgress.kanjiUserNotes !== 'object' || userProgress.kanjiUserNotes === null) {
            userProgress.kanjiUserNotes = {};
        }
    }

    function saveUserProgressLocal() {
        localStorage.setItem('fusokuDictionaryProgress', JSON.stringify(userProgress));
        // updateCollectionsCountSidebar(); // REMOVED
    }


    // ========== LÓGICA DE BÚSQUEDA ==========
    async function buscarPalabra(query) {
        if (!query) return [];

        // llama al endpoint PHP con el parámetro q
        const url = `search.php?q=${encodeURIComponent(query)}`;

        try {
            const response = await fetch(url);
            if (!response.ok) throw new Error('Error en la consulta');
            const results = await response.json();
            return results;
        } catch (error) {
            console.error(error);
            return [];
        }
    }
    function performSearch(forceNonJapaneseSearch = false) {
        window.scrollTo(0, 0); 
        const currentSearchInputValue = searchInput.value.trim(); 
        if (!currentSearchInputValue) {
            clearSearchResults();
            if (searchResultsContainer) searchResultsContainer.style.display = 'none';
            if (searchResultsTabsContainer) searchResultsTabsContainer.style.display = 'none';
            if (dictionaryInitialMessage) dictionaryInitialMessage.style.display = 'block';
            return;
        }

        if (dictionaryInitialMessage) dictionaryInitialMessage.style.display = 'none';
        if (searchResultsContainer) searchResultsContainer.style.display = 'flex'; 
        handleResponsiveTabs(); 

        navigationHistory = []; 
        showView('main'); 

        const originalQueryForDetection = currentSearchInputValue;
        let queryForSearch = originalQueryForDetection;
        let searchAsJapanese = false;
        let queryWasConverted = false;

        if (forceNonJapaneseSearch) {
            searchAsJapanese = false;
        } else if (originalQueryForDetection.toLowerCase().includes('x') || originalQueryForDetection.toLowerCase().includes('cc')) {
            searchAsJapanese = false;
        } else { 
            const wanakanaInfoOriginal = {
                isJapanese: wanakana.isJapanese(originalQueryForDetection),
                isRomaji: wanakana.isRomaji(originalQueryForDetection)
            };

            if (wanakanaInfoOriginal.isJapanese) {
                searchAsJapanese = true;
            } else if (wanakanaInfoOriginal.isRomaji) {
                const lowerQuery = originalQueryForDetection.toLowerCase();
                let treatAsJapaneseRomaji;
                const strongRomajiPattern = /tsu|shi|chi|kk|tt|ss|pp|tch|dzu|j[auo]|ky[auo]|gy[auo]|sh[auo]|ch[auo]|ny[auo]|hy[auo]|my[auo]|ry[auo]|by[auo]|py[auo]|n$|n(?![aiueo])/i.test(lowerQuery);
                const nonJapaneseConsonantClusterPattern = /(mb|mp|br|bl|pl|pr|tr|dr|cr|cl|fr|fl|gr|gl|str|spl|spr|scr|pt|ct|gd|ks|ps|sth|phth|mn|gn|kn|pn|ps|rh|wr)/i.test(lowerQuery);
                const hasSpanishCaCoCu = /c[aou]/i.test(originalQueryForDetection) && !/ch/i.test(originalQueryForDetection);
                const hasK = /k/i.test(lowerQuery);
                const hasL = /l/i.test(lowerQuery);
                const hasV = /v/i.test(lowerQuery);
                const hasRR = /rr/i.test(lowerQuery);
                const hasQU = /qu/i.test(lowerQuery);
                const hasStandaloneHu = /h(?=u)/i.test(lowerQuery) && !/f(?=u)/i.test(lowerQuery) && !(/sh(?=u)|ch(?=u)|hy(?=u)|ky(?=u)|gy(?=u)|ny(?=u)|my(?=u)|ry(?=u)|by(?=u)|py(?=u)/i.test(lowerQuery));

                if (hasL || hasV || hasRR || hasQU || hasStandaloneHu) {
                    treatAsJapaneseRomaji = false;
                } else if (strongRomajiPattern) {
                    treatAsJapaneseRomaji = true;
                } else if (nonJapaneseConsonantClusterPattern) {
                    treatAsJapaneseRomaji = false;
                } else if (hasSpanishCaCoCu && !hasK) {
                    treatAsJapaneseRomaji = false;
                } else {
                    treatAsJapaneseRomaji = true;
                }

                if (treatAsJapaneseRomaji) {
                    const hiraganaAttempt = wanakana.toHiragana(originalQueryForDetection, { customKanaMapping: { ou: 'おう', oo: 'おお', ee: 'ええ' } });
                    if (!wanakana.isKana(hiraganaAttempt)) {
                        searchAsJapanese = false;
                        queryWasConverted = false;
                    } else {
                        queryForSearch = hiraganaAttempt;
                        searchAsJapanese = true;
                        queryWasConverted = true;
                    }
                } else {
                    searchAsJapanese = false;
                }
            } else {
                searchAsJapanese = false;
            }
        }

        searchSuggestionContainer.style.display = 'none';

        let vocabResults = [];
        let kanjiResults = [];
        const matchedKanjiLiterals = new Set(); 


// Fragmento adaptado para uso SQL (vía PHP backend), manteniendo el mismo comportamiento
// Asegúrate de que la URL base de la API sea la correcta para tu servidor Node.js.
if (searchAsJapanese) {
  const hiraganaQuery = queryWasConverted
    ? queryForSearch
    : wanakana.toHiragana(queryForSearch.toString(), { // Asegurar que queryForSearch es string
        customKanaMapping: { ou: 'おう', oo: 'おお', ee: 'ええ' }
      });
  const katakanaQuery = wanakana.toKatakana(hiraganaQuery);
  const originalQueryForDetection = queryForSearch;

  fetch(`/api/search?q=${encodeURIComponent(queryForSearch.toString())}`)
    .then(r => r.json())
    .then(results => {
      const vocabResultsRaw = results.map(result => {
        const kj = (result.KJ_tx || '').trim();
        const kn = (result.KN_tx || '').trim();

        const isExact =
          kj === originalQueryForDetection ||
          kn === originalQueryForDetection ||
          kn === hiraganaQuery ||
          kn === katakanaQuery;

        const isPartial =
          kj.includes(originalQueryForDetection) ||
          kn.includes(hiraganaQuery) ||
          kn.includes(katakanaQuery);

        return {
          ...result,
          isExactMatch: isExact ? 2 : isPartial ? 1 : 0
        };
      }).filter(entry => entry.isExactMatch > 0);

      const uniqueResults = vocabResultsRaw.filter((entry, index, self) =>
        index === self.findIndex(e => e.id === entry.id)
      );

      vocabResults = uniqueResults.sort(sortVocabResults);
      renderPaginatedVocabResults();
    })
    .catch(error => {
      console.error("Error buscando en japonés:", error);
    });
} else {
  const normalizedQuery = originalQueryForDetection.toLowerCase();
  fetch(`/api/search?q=${encodeURIComponent(normalizedQuery)}`)
    .then(r => r.json())
    .then(results => {
      const vocabResultsRaw = results.map(result => {
        const gl1 = (result.SE_GL_1_tx || '').trim().toLowerCase();
        const gl2 = (result.SE_GL_2_tx || '').trim().toLowerCase();

        const exactMatch = gl1 === normalizedQuery || gl2 === normalizedQuery;
        const regex = new RegExp(`\\b${normalizedQuery}\\b`, 'i');
        const partialMatch = regex.test(gl1) || regex.test(gl2);

        return {
          ...result,
          isExactMatch: exactMatch ? 2 : partialMatch ? 1 : 0
        };
      }).filter(entry => entry.isExactMatch > 0);

      const uniqueResults = vocabResultsRaw.filter((entry, index, self) =>
        index === self.findIndex(e => e.id === entry.id)
      );

      vocabResults = uniqueResults.sort(sortVocabResults);
      renderPaginatedVocabResults();
    })
    .catch(error => {
      console.error("Error buscando en ES/EN:", error);
    });
}



function sortVocabResults(a, b) {
  const jlptOrder = { 'N5': 1, 'N4': 2, 'N3': 3, 'N2': 4, 'N1': 5 };

  if (a.isExactMatch !== b.isExactMatch)
    return b.isExactMatch - a.isExactMatch;

  const aLevel = jlptOrder[a.jlptLevel] || 6;
  const bLevel = jlptOrder[b.jlptLevel] || 6;
  if (aLevel !== bLevel)
    return aLevel - bLevel;

  const aCommon = a.KJ.some(k => k.co) || a.KN.some(k => k.co);
  const bCommon = b.KJ.some(k => k.co) || b.KN.some(k => k.co);
  return bCommon - aCommon;
}



    function sortKanjiResults(a, b) {
        const jlptOrder = { 5: 1, 4: 2, 3: 3, 2: 4, 1: 5 }; 
        const aJlptData = kanjiJsonData[a.l]; 
        const bJlptData = kanjiJsonData[b.l]; 
        const aJlpt = aJlptData?.jlpt_new ? jlptOrder[aJlptData.jlpt_new] : 6;
        const bJlpt = bJlptData?.jlpt_new ? jlptOrder[bJlptData.jlpt_new] : 6;
        if (aJlpt !== bJlpt) return aJlpt - bJlpt;
        const aFreq = a.ms?.fr || Infinity; 
        const bFreq = b.ms?.fr || Infinity; 
        return aFreq - bFreq;
    }


    // ========== RENDERIZADO DE RESULTADOS DE BÚSQUEDA (PAGINADO) ==========
    function renderPaginatedVocabResults() {
        vocabResultsTitle.textContent = `Vocabulario — (${currentVocabResults.length})`;
        const itemsToRender = currentVocabResults.slice(currentVocabOffset, currentVocabOffset + ITEMS_PER_LOAD);

        if (itemsToRender.length === 0 && currentVocabOffset === 0) {
            vocabResultsList.innerHTML = '<p class="no-results">No se encontraron palabras.</p>';
            vocabShowMoreButton.style.display = 'none';
            return;
        }

        itemsToRender.forEach(entry => renderSingleVocabItem(entry, vocabResultsList));
        currentVocabOffset += itemsToRender.length;

        vocabShowMoreButton.style.display = currentVocabOffset < currentVocabResults.length ? 'block' : 'none';
    }

    function renderPaginatedKanjiResults() {
        kanjiResultsTitle.textContent = `Kanji — (${currentKanjiResults.length})`;
        const itemsToRender = currentKanjiResults.slice(currentKanjiOffset, currentKanjiOffset + ITEMS_PER_LOAD);

        if (itemsToRender.length === 0 && currentKanjiOffset === 0) {
            kanjiResultsList.innerHTML = '<p class="no-results">No se encontraron kanjis.</p>';
            kanjiShowMoreButton.style.display = 'none';
            return;
        }

        itemsToRender.forEach(kEntry => renderSingleKanjiItem(kEntry, kanjiResultsList));
        currentKanjiOffset += itemsToRender.length;

        kanjiShowMoreButton.style.display = currentKanjiOffset < currentKanjiResults.length ? 'block' : 'none';
    }

    function renderSingleVocabItem(entry, parentListElement) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'vocab-item';
        itemDiv.dataset.vocabId = entry.id; 

        const headerDiv = document.createElement('div');
        headerDiv.className = 'vocab-item-header';
        const termH3 = document.createElement('h3');
        termH3.className = 'vocab-term';
        termH3.innerHTML = generateTermDisplayHtml(entry).html;
        headerDiv.appendChild(termH3);
        itemDiv.appendChild(headerDiv);

        const tagsDiv = document.createElement('div');
        tagsDiv.className = 'vocab-tags';
        getTagsForEntry(entry).forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.className = `tag ${tag.class}`;
            tagSpan.textContent = tag.text;
            tagsDiv.appendChild(tagSpan);
        });
        itemDiv.appendChild(tagsDiv);

        const sensesOl = document.createElement('ol');
        sensesOl.className = 'vocab-senses';

        const hasSpanishGlossOverall = entry.SE.some(s => s.GL.some(g => g.ln === 'spa'));
        let displayedSenseCount = 0;
        let senseDisplayNumber = 1;

        for (const sense of entry.SE) {
            if (displayedSenseCount >= 3) break;

            let glossToDisplay = null;
            if (hasSpanishGlossOverall) {
                const spanishGloss = sense.GL.find(g => g.ln === 'es');
                if (spanishGloss) glossToDisplay = spanishGloss.tx;
            } else {
                const englishGloss = sense.GL.find(g => g.ln === 'en');
                if (englishGloss) glossToDisplay = englishGloss.tx;
            }

            if (glossToDisplay) {
                const senseLi = document.createElement('li');
                senseLi.className = 'vocab-sense';
                const glossP = document.createElement('p');
                glossP.className = 'vocab-gloss';
                
                let cleanedGloss = glossToDisplay.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, '');
                glossP.innerHTML = `<strong class="sense-number-dict">${senseDisplayNumber}.</strong> <span class="sense-text-content">${cleanedGloss}</span>`;
                
                senseLi.appendChild(glossP);
                sensesOl.appendChild(senseLi);
                displayedSenseCount++;
                senseDisplayNumber++;
            }
        }

        if (displayedSenseCount === 0) {
            sensesOl.innerHTML = '<li><p class="vocab-gloss"><strong class="sense-number-dict">1.</strong> <span class="sense-text-content"><em>No hay definiciones disponibles.</em></span></p></li>';
        }
        itemDiv.appendChild(sensesOl);
        
        const tempDiv = document.createElement('div');
        tempDiv.style.visibility = 'hidden';
        tempDiv.style.position = 'absolute';
        tempDiv.appendChild(sensesOl.cloneNode(true));
        document.body.appendChild(tempDiv);
        if (tempDiv.querySelector('ol').scrollHeight > 100) { 
             sensesOl.classList.add('apply-fade');
        } else {
             sensesOl.classList.remove('apply-fade'); 
             sensesOl.style.maxHeight = 'none'; 
        }
        document.body.removeChild(tempDiv);

        itemDiv.addEventListener('click', () => showView('vocabDetail', entry.id));
        parentListElement.appendChild(itemDiv);
    }

    function renderSingleKanjiItem(kEntry, parentListElement) {
        const itemDiv = document.createElement('div');
        itemDiv.className = 'kanji-item';
        itemDiv.dataset.kanjiChar = kEntry.l; 

        const jlptData = kanjiJsonData[kEntry.l]; 
        if (jlptData?.jlpt_new) {
            const jlptBadge = document.createElement('div');
            jlptBadge.className = 'kanji-item-jlpt-badge';
            jlptBadge.textContent = `N${jlptData.jlpt_new}`;
            itemDiv.appendChild(jlptBadge);
        }
        
        const charContainerDiv = document.createElement('div');
        charContainerDiv.className = 'kanji-item-char-container';
        const charSpan = document.createElement('span');
        charSpan.className = 'kanji-item-char';
        charSpan.textContent = kEntry.l; 
        charContainerDiv.appendChild(charSpan);
        itemDiv.appendChild(charContainerDiv);

        const infoDiv = document.createElement('div');
        infoDiv.className = 'kanji-item-info';

        const meaningP = document.createElement('p');
        meaningP.className = 'kanji-item-meaning';
        let spanishMeaning, englishMeaning;
        if (kEntry.rm && kEntry.rm.g && kEntry.rm.g[0] && kEntry.rm.g[0].mn) {
            spanishMeaning = kEntry.rm.g[0].mn.find(m => m.ln === 'es');
            englishMeaning = kEntry.rm.g[0].mn.find(m => m.ln === 'en');
        }
        meaningP.textContent = spanishMeaning ? spanishMeaning.v : (englishMeaning ? englishMeaning.v : 'N/A'); 
        infoDiv.appendChild(meaningP);
        
        const readingsDiv = document.createElement('div');
        readingsDiv.className = 'kanji-item-readings';
        let onReadings = 'N/A';
        let kunReadings = 'N/A';

        if (kEntry.rm && kEntry.rm.g && kEntry.rm.g[0] && kEntry.rm.g[0].rd) {
            onReadings = kEntry.rm.g[0].rd.filter(r => r.t === 'ja_on').map(r => r.v).join(', ') || 'N/A';
            kunReadings = kEntry.rm.g[0].rd.filter(r => r.t === 'ja_kun').map(r => r.v).join(', ') || 'N/A';
        }

        readingsDiv.innerHTML = `<p><strong>On:</strong> ${onReadings}</p><p><strong>Kun:</strong> ${kunReadings}</p>`;
        infoDiv.appendChild(readingsDiv);

        itemDiv.appendChild(infoDiv);
        itemDiv.addEventListener('click', () => showView('kanjiDetail', kEntry.l)); 
        parentListElement.appendChild(itemDiv);
    }

    function clearSearchResults() {
        vocabResultsList.innerHTML = '';
        kanjiResultsList.innerHTML = '';
        vocabResultsTitle.textContent = 'Vocabulario — (0)';
        kanjiResultsTitle.textContent = 'Kanji — (0)';
        vocabShowMoreButton.style.display = 'none';
        kanjiShowMoreButton.style.display = 'none';
    }

    function handleResponsiveTabs() {
        if (window.innerWidth <= 768) {
            searchResultsTabsContainer.style.display = 'flex';
            document.getElementById('vocabulary-results-section').classList.add('active-results-section-mobile');
            document.getElementById('kanji-results-section').classList.remove('active-results-section-mobile');
            searchResultsTabButtons.forEach(btn => btn.classList.remove('active-tab'));
            searchResultsTabButtons[0].classList.add('active-tab'); 
        } else {
            searchResultsTabsContainer.style.display = 'none';
            document.getElementById('vocabulary-results-section').classList.remove('active-results-section-mobile');
            document.getElementById('kanji-results-section').classList.remove('active-results-section-mobile');
        }
    }


    // ========== LÓGICA DE LAS VISTAS DE DETALLE ==========
    function showView(viewName, dataId, isBackNavigation = false) {
        console.log(`showView called with: viewName='${viewName}', dataId='${dataId}', isBackNavigation=${isBackNavigation}`);
        mainView.classList.remove('active-dict-view');
        vocabDetailView.classList.remove('active-dict-view');
        kanjiDetailView.classList.remove('active-dict-view');
        // collectionsView.classList.remove('active-dict-view'); // REMOVED

        if (!isBackNavigation) {
            navigationHistory.push({ view: viewName, id: dataId });
        }

        if (viewName === 'main') {
            mainView.classList.add('active-dict-view');
        } else if (viewName === 'vocabDetail') {
            currentDetailTerm = jmdictData.find(e => e.id === dataId);
            if (currentDetailTerm) renderVocabDetail(currentDetailTerm);
            if (!isBackNavigation) {
                window.location.hash = 'type=wordJmdict&id=' + encodeURIComponent(dataId);
            }
            vocabDetailView.classList.add('active-dict-view');
        } else if (viewName === 'kanjiDetail') {
            console.log(`Attempting to show kanjiDetail for ID: ${dataId}`);
            currentDetailKanji = kanjidicData[dataId]; 
            console.log('currentDetailKanji from kanjidicData:', currentDetailKanji); 
            if (currentDetailKanji) {
                console.log('Calling renderKanjiDetail for:', currentDetailKanji.l);
                renderKanjiDetail(currentDetailKanji);
            } else {
                console.error(`Kanji with ID ${dataId} not found in kanjidicData.`);
            }
            if (!isBackNavigation) {
                window.location.hash = 'type=kanjiKanjidic&id=' + encodeURIComponent(dataId);
            }
            kanjiDetailView.classList.add('active-dict-view');
            console.log('kanjiDetailView active class added. Current classes:', kanjiDetailView.className);
        } 
        // else if (viewName === 'collections') { // REMOVED
        //     renderCollectionsView();
        //     collectionsView.classList.add('active-dict-view');
        // }
        window.scrollTo(0, 0);
    }

    function goBack() {
        if (navigationHistory.length <= 1) {
            navigationHistory = []; 
            showView('main', null, false); 
            return;
        }
        navigationHistory.pop(); 
        const previousState = navigationHistory[navigationHistory.length - 1]; 
        if (previousState) {
            showView(previousState.view, previousState.id, true); 
        } else {
            showView('main', null, false); 
        }
    }
    
    function renderVocabDetail(entry) {
        const termDisplayResult = generateTermDisplayHtml(entry);
        vocabDetailTermDisplay.innerHTML = `<h3 class="vocab-term">${termDisplayResult.html}</h3>`;

        const existingPocoComunDiv = vocabDetailTermDisplayWrapper.querySelector('.poco-comun-kanji-dict');
        if (existingPocoComunDiv) {
            existingPocoComunDiv.remove();
        }

        if (termDisplayResult.otherForms && termDisplayResult.otherForms.length > 0) {
            const pocoComunDiv = document.createElement('div');
            pocoComunDiv.className = 'poco-comun-kanji-dict';
            pocoComunDiv.innerHTML = `Poco común: ${termDisplayResult.otherForms.join('、')}`;
            vocabDetailTermDisplay.insertAdjacentElement('afterend', pocoComunDiv);
        }
        
        vocabDetailTagsContainer.innerHTML = '';
        getTagsForEntry(entry).forEach(tag => {
            const tagSpan = document.createElement('span');
            tagSpan.className = `tag ${tag.class}`;
            tagSpan.textContent = tag.text;
            vocabDetailTagsContainer.appendChild(tagSpan);
        });
    
        vocabDetailSensesList.innerHTML = '';
        let spanishSensesFound = false;
        let senseNumberDetail = 1;
        entry.SE.forEach(sense => {
            const spanishGlossObjects = sense.GL.filter(g => g.ln === 'es');
            if (spanishGlossObjects.length > 0) {
                const spanishGlossText = spanishGlossObjects.map(g => g.tx).join('; ');
                const senseDiv = document.createElement('div');
                senseDiv.className = 'vocab-detail-sense-item';
                const glossP = document.createElement('p');
                glossP.className = 'vocab-gloss';
                
                let tagHtml = '';
                if (sense.ms && sense.ms.includes('uk')) {
                    const ukTagSpan = document.createElement('span'); 
                    ukTagSpan.className = 'tag tag-kana-only sense-uk-tag';
                    ukTagSpan.textContent = 'Kana';
                    tagHtml = ukTagSpan.outerHTML + '&nbsp;'; 
                }

                let senseInfoText = '';
                if (sense.in && sense.in.length > 0) {
                    senseInfoText = ` <span class="sense-info-dict">(${sense.in.join('; ')})</span>`;
                }
                const cleanedGloss = spanishGlossText.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, '');
                glossP.innerHTML = `<strong class="sense-number-dict">${senseNumberDetail}.</strong> <span class="sense-text-content">${tagHtml}${cleanedGloss}${senseInfoText}</span>`;

                senseDiv.appendChild(glossP);
                vocabDetailSensesList.appendChild(senseDiv);
                spanishSensesFound = true;
                senseNumberDetail++;
            }
        });
    
        vocabDetailEnglishSensesList.innerHTML = '';
        let englishSensesFound = false;
        let englishSenseNumberDetail = 1;
        entry.SE.forEach(sense => {
            const englishGlossObjects = sense.GL.filter(g => g.ln === 'en');
            if (englishGlossObjects.length > 0) {
                const englishGlossText = englishGlossObjects.map(g => g.tx).join('; ');
                const senseDiv = document.createElement('div');
                senseDiv.className = 'vocab-detail-sense-item';
                const glossP = document.createElement('p');
                glossP.className = 'vocab-gloss';

                let tagHtmlEng = '';
                if (sense.ms && sense.ms.includes('uk')) {
                    const ukTagSpan = document.createElement('span');
                    ukTagSpan.className = 'tag tag-kana-only sense-uk-tag';
                    ukTagSpan.textContent = 'Kana';
                    tagHtmlEng = ukTagSpan.outerHTML + '&nbsp;';
                }

                let senseInfoTextEng = '';
                if (sense.in && sense.in.length > 0) {
                    senseInfoTextEng = ` <span class="sense-info-dict">(${sense.in.join('; ')})</span>`;
                }
                const cleanedGlossEng = englishGlossText.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, '');
                glossP.innerHTML = `<strong class="sense-number-dict">${englishSenseNumberDetail}.</strong> <span class="sense-text-content">${tagHtmlEng}${cleanedGlossEng}${senseInfoTextEng}</span>`;

                senseDiv.appendChild(glossP);

                if (spanishSensesFound) { 
                    vocabDetailEnglishSensesList.appendChild(senseDiv);
                } else { 
                    vocabDetailSensesList.appendChild(senseDiv);
                }
                englishSensesFound = true;
                englishSenseNumberDetail++;
            }
        });
    
        if (spanishSensesFound) {
            vocabDetailSensesTitle.textContent = 'Definiciones en Español';
            vocabDetailSensesContainer.style.display = 'block';
            if (englishSensesFound) {
                vocabDetailEnglishSensesContainer.style.display = 'block';
            } else {
                vocabDetailEnglishSensesContainer.style.display = 'none';
            }
        } else if (englishSensesFound) {
            vocabDetailSensesTitle.textContent = 'Definiciones en Inglés';
            vocabDetailSensesContainer.style.display = 'block';
            vocabDetailEnglishSensesContainer.style.display = 'none';
        } else { 
            vocabDetailSensesTitle.textContent = 'Definiciones';
            vocabDetailSensesList.innerHTML = '<p><strong class="sense-number-dict">1.</strong> <span class="sense-text-content"><em>No hay definiciones disponibles.</em></span></p>';
            vocabDetailSensesContainer.style.display = 'block';
            vocabDetailEnglishSensesContainer.style.display = 'none';
        }

        vocabDetailKanjiGrid.innerHTML = '';
        const uniqueKanjisInTerm = new Set();
        if (entry.KJ[0]?.tx) {
            entry.KJ[0].tx.split('').forEach(char => {
                if (wanakana.isKanji(char)) uniqueKanjisInTerm.add(char);
            });
        }
        if (termDisplayResult.otherForms) {
            termDisplayResult.otherForms.forEach(form => {
                form.split('').forEach(char => {
                    if (wanakana.isKanji(char)) uniqueKanjisInTerm.add(char);
                });
            });
        }

        uniqueKanjisInTerm.forEach(kChar => {
            const kanjiDiv = document.createElement('div');
            kanjiDiv.className = 'kanji-item-char'; 
            kanjiDiv.textContent = kChar;
            kanjiDiv.addEventListener('click', () => showView('kanjiDetail', kChar));
            vocabDetailKanjiGrid.appendChild(kanjiDiv);
        });
        
        const displayTermForModalAndActions = entry.KJ[0]?.tx || entry.KN[0]?.tx;
        
        currentExampleOffset = 0;
        vocabDetailExamplesList.innerHTML = ''; 

        const primarySearchTerms = [];
        const mainKanjiFormForExamples = entry.KJ[0]?.tx;
        const primaryKanaObjForExamples = entry.KN.find(k => k.co) || entry.KN[0];
        const mainKanaFormForExamples = primaryKanaObjForExamples?.tx;
        const allKanjiFormsForExamples = entry.KJ.map(k => k.tx).filter(Boolean);
        const isUsuallyKanaForExamples = shouldDisplayAsUsuallyKana(entry, allKanjiFormsForExamples, primaryKanaObjForExamples);

        if (mainKanjiFormForExamples && !isUsuallyKanaForExamples) {
            primarySearchTerms.push(mainKanjiFormForExamples);
        } else {
            if (mainKanjiFormForExamples) {
                primarySearchTerms.push(mainKanjiFormForExamples);
            }
            if (mainKanaFormForExamples) {
                if (!primarySearchTerms.includes(mainKanaFormForExamples)) { 
                    primarySearchTerms.push(mainKanaFormForExamples);
                }
            }
        }
        if (primarySearchTerms.length === 0 && mainKanaFormForExamples) {
             primarySearchTerms.push(mainKanaFormForExamples);
        }
        if (primarySearchTerms.length === 0 && mainKanjiFormForExamples) {
            primarySearchTerms.push(mainKanjiFormForExamples);
        }

        currentExampleSentences = [];
        if (primarySearchTerms.length > 0) {
            let filteredExamples = tatoebaSentences.filter(s => {
                if (!s || !s.jp) return false;
                return primarySearchTerms.some(term => s.jp.includes(term));
            });
            currentExampleSentences = Array.from(new Map(filteredExamples.map(ex => [ex.jp, ex])).values());
        }
        
        if (vocabDetailExamplesTitle) {
            vocabDetailExamplesTitle.textContent = `Oraciones de Ejemplo (${currentExampleSentences.length})`;
        }
        renderPaginatedExamples();


        currentItemForModal = { type: 'vocab', id: entry.id, name: displayTermForModalAndActions };
        updateVocabLikeButtonVisual();
    }
    
    function renderPaginatedExamples() {
        const itemsToRender = currentExampleSentences.slice(currentExampleOffset, currentExampleOffset + ITEMS_PER_LOAD);
        
        if (itemsToRender.length === 0 && currentExampleOffset === 0) {
            vocabDetailExamplesList.innerHTML = '<p><em>No hay ejemplos disponibles.</em></p>';
            vocabExamplesShowMoreButton.style.display = 'none';
            return;
        }

        let termsToHighlightInExample = [];
        if (currentDetailTerm) { 
            const mainKanjiFormHighlight = currentDetailTerm.KJ[0]?.tx;
            const primaryKanaObjHighlight = currentDetailTerm.KN.find(k => k.co) || currentDetailTerm.KN[0];
            const mainKanaFormHighlight = primaryKanaObjHighlight?.tx;
            const allKanjiFormsHighlight = currentDetailTerm.KJ.map(k => k.tx).filter(Boolean);
            const isUsuallyKanaHighlight = shouldDisplayAsUsuallyKana(currentDetailTerm, allKanjiFormsHighlight, primaryKanaObjHighlight);

            if (mainKanjiFormHighlight && !isUsuallyKanaHighlight) {
                termsToHighlightInExample.push(mainKanjiFormHighlight);
            } else {
                if (mainKanjiFormHighlight) {
                    termsToHighlightInExample.push(mainKanjiFormHighlight);
                }
                if (mainKanaFormHighlight) {
                    if (!termsToHighlightInExample.includes(mainKanaFormHighlight)) {
                        termsToHighlightInExample.push(mainKanaFormHighlight);
                    }
                }
                if (termsToHighlightInExample.length === 0) {
                    const anyKanji = currentDetailTerm.KJ.find(k => k.tx)?.tx;
                    const anyKana = currentDetailTerm.KN.find(k => k.tx)?.tx;
                    if (anyKanji) termsToHighlightInExample.push(anyKanji);
                    if (anyKana && !termsToHighlightInExample.includes(anyKana)) termsToHighlightInExample.push(anyKana);
                }
            }
            termsToHighlightInExample = [...new Set(termsToHighlightInExample.filter(Boolean))].sort((a, b) => b.length - a.length);
        }

        itemsToRender.forEach(ex => {
            const exDiv = document.createElement('div');
            exDiv.className = 'sense-example'; 
            let highlightedJp = ex.jp;
            termsToHighlightInExample.forEach(termToHighlight => {
                if (termToHighlight) { 
                    const escapedTerm = termToHighlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
                    const regex = new RegExp(escapedTerm, 'g');
                    highlightedJp = highlightedJp.replace(regex, `<strong>$&</strong>`);
                }
            });
            exDiv.innerHTML = `<p class="example-jp">${highlightedJp}</p><p class="example-es">${ex.es}</p>`;
            vocabDetailExamplesList.appendChild(exDiv);
        });
        currentExampleOffset += itemsToRender.length;

        vocabExamplesShowMoreButton.style.display = currentExampleOffset < currentExampleSentences.length ? 'block' : 'none';
    }


    function renderKanjiDetail(kEntry) {
        console.log('renderKanjiDetail called with kEntry:', kEntry);
        if (!kEntry || !kEntry.l) {
            console.error('renderKanjiDetail called with invalid kEntry:', kEntry);
            return;
        }
        currentDetailKanji = kEntry;
        kanjiDetailCharForData.textContent = kEntry.l; 

        const spanishMeaningHeader = kEntry.rm?.g[0]?.mn.find(m => m.ln === 'es'); 
        const englishMeaningHeader = kEntry.rm?.g[0]?.mn.find(m => m.ln === 'en'); 
        kanjiDetailMainMeaningHeader.textContent = spanishMeaningHeader ? spanishMeaningHeader.v : (englishMeaningHeader ? englishMeaningHeader.v : ''); 

        initDmakDict(kEntry.l); 

        const kjJson = kanjiJsonData[kEntry.l] || {}; 
        kanjiDetailJlpt.textContent = kjJson.jlpt_new ? `N${kjJson.jlpt_new}` : 'N/A';
        kanjiDetailFrequency.textContent = kEntry.ms?.fr || 'N/A'; 
        kanjiDetailStrokes.textContent = kEntry.ms?.sc[0] || 'N/A'; 
        
        const onReadings = kEntry.rm?.g.flatMap(group => group.rd.filter(r => r.t === 'ja_on').map(r => r.v)).join(', ') || 'N/A'; 
        const kunReadings = kEntry.rm?.g.flatMap(group => group.rd.filter(r => r.t === 'ja_kun').map(r => r.v)).join(', ') || 'N/A'; 
        kanjiDetailOnYomi.textContent = onReadings;
        kanjiDetailKunYomi.textContent = kunReadings;

        kanjiMeaningsTab.innerHTML = ''; 
        let meaningNumber = 1; 
        let meaningsFound = false;
        kEntry.rm?.g.forEach(group => { 
            group.mn.forEach(m => { 
                if (m.ln === 'es' || (m.ln === 'en' && !group.mn.some(mx => mx.ln === 'es'))) { 
                    const p = document.createElement('p');
                    const cleanedMeaning = m.v.replace(/^\s*[\d①-⑨]+\s*[\.\)]?\s*/, ''); 
                    p.innerHTML = `<strong>${meaningNumber}.</strong> <span class="sense-text-content">${cleanedMeaning}${m.ln === 'en' ? ' (en)' : ''}</span>`; 
                    kanjiMeaningsTab.appendChild(p);
                    meaningNumber++;
                    meaningsFound = true;
                }
            });
        });
        if (!meaningsFound) { 
            kanjiMeaningsTab.innerHTML = '<p><em>No hay significados disponibles.</em></p>';
        }

        kanjiRadicalsTab.innerHTML = ''; 
        const components = kradfileData[kEntry.l] || []; 
        if (components.length > 0) {
            components.forEach(compSymbol => {
                if (compSymbol === kEntry.l) return; 
                const compSpan = document.createElement('span');
                compSpan.className = 'krad-component-link kanji-char'; 
                compSpan.textContent = compSymbol;
                compSpan.title = `Ver detalle de ${compSymbol}`;
                compSpan.addEventListener('click', () => {
                    const componentIsKanji = kanjidicData[compSymbol];
                    if (componentIsKanji) {
                        showView('kanjiDetail', compSymbol);
                    } else {
                        console.warn(`Componente ${compSymbol} no es un kanji principal en KANJIDIC.`);
                    }
                });
                kanjiRadicalsTab.appendChild(compSpan);
                kanjiRadicalsTab.appendChild(document.createTextNode(' '));
            });
        } else {
            kanjiRadicalsTab.innerHTML = '<p><em>No hay datos de componentes.</em></p>';
        }
        
        kanjiUserNotesTextarea.value = userProgress.kanjiUserNotes[kEntry.l] || ''; 
        kanjiUserNotesTextarea.readOnly = true;
        kanjiSaveNotesButton.classList.add('hidden');
        kanjiEditNotesButton.classList.remove('hidden');
        kanjiResetNotesButton.disabled = !userProgress.kanjiUserNotes[kEntry.l]; 

        currentKanjiCommonWordsOffset = 0;
        currentKanjiAllWordsOffset = 0;
        kanjiCommonWordsList.innerHTML = ''; 
        kanjiAllWordsList.innerHTML = '';

        currentKanjiCommonWords = jmdictData.filter(e =>
            (e.KJ[0]?.tx?.includes(kEntry.l) || e.KN[0]?.tx?.includes(kEntry.l)) &&
            
            (e.KJ.some(k => k.co) || e.KN.some(k => k.co))
        ).sort(sortVocabResults);

        currentKanjiAllWords = jmdictData.filter(e =>
            e.KJ[0]?.tx?.includes(kEntry.l) || e.KN[0]?.tx?.includes(kEntry.l)
        ).sort(sortVocabResults);
        
        renderPaginatedKanjiRelatedWords('common');
        renderPaginatedKanjiRelatedWords('all');
        
        currentItemForModal = { type: 'kanji', id: kEntry.l, name: kEntry.l };
        updateKanjiLikeButtonVisual();
    }
    
    function renderPaginatedKanjiRelatedWords(type) {
        let listElement, wordsArray, offset, showMoreButton;

        if (type === 'common') {
            listElement = kanjiCommonWordsList; 
            wordsArray = currentKanjiCommonWords;
            offset = currentKanjiCommonWordsOffset;
            showMoreButton = kanjiCommonWordsShowMoreButton;
        } else { 
            listElement = kanjiAllWordsList;
            wordsArray = currentKanjiAllWords;
            offset = currentKanjiAllWordsOffset;
            showMoreButton = kanjiAllWordsShowMoreButton;
        }

        const itemsToRender = wordsArray.slice(offset, offset + ITEMS_PER_LOAD);

        if (itemsToRender.length === 0 && offset === 0) {
            listElement.innerHTML = '<p class="no-results"><em>No se encontraron palabras.</em></p>';
            showMoreButton.style.display = 'none';
            return;
        }

        itemsToRender.forEach(entry => {
            renderSingleVocabItem(entry, listElement); 
        });
        
        if (type === 'common') {
            currentKanjiCommonWordsOffset += itemsToRender.length;
            showMoreButton.style.display = currentKanjiCommonWordsOffset < wordsArray.length ? 'block' : 'none';
        } else {
            currentKanjiAllWordsOffset += itemsToRender.length;
            showMoreButton.style.display = currentKanjiAllWordsOffset < wordsArray.length ? 'block' : 'none';
        }
    }


    // ========== FUNCIONES AUXILIARES (Furigana, Tags, Dmak) ==========

    function shouldDisplayAsUsuallyKana(entry, kanjiForms, primaryKanaObj) { 
        const firstSense = entry.SE?.[0];
        if (!firstSense) {
            return false; 
        }
        return firstSense.ms?.includes('uk') || false;
    }

    function generateTermDisplayHtml(entry) {
        const primaryKanaObj = entry.KN.find(k => k.co) || entry.KN[0];
        const primaryKanaText = primaryKanaObj?.tx;
        const kanjiInitiallyFilteredByIdenticalKanaExclusion = (entry.KJ || [])
            .filter(k_ele => k_ele.tx && k_ele.tx !== primaryKanaText);
        const SEARCH_ONLY_KANJI_TAG = "sK"; 
        const kanjiForDisplayConsideration = kanjiInitiallyFilteredByIdenticalKanaExclusion
            .filter(k_ele => !(k_ele.tg && k_ele.tg.includes(SEARCH_ONLY_KANJI_TAG)));
        const rKForms = kanjiForDisplayConsideration.filter(k_ele => k_ele.tg && k_ele.tg.includes("rK")); 
        const nonRKForms = kanjiForDisplayConsideration.filter(k_ele => !(k_ele.tg && k_ele.tg.includes("rK")));
        const displayAsUk = shouldDisplayAsUsuallyKana(entry, nonRKForms.map(k => k.tx), primaryKanaObj);
        let mainDisplayKanjiElements = [];
        if (nonRKForms.length > 0) {
            mainDisplayKanjiElements = nonRKForms.filter(k_ele =>
                k_ele.co && !(k_ele.tg && k_ele.tg.includes(USUALLY_KANA_WRITING_TAG))
            );
            if (mainDisplayKanjiElements.length === 0) { 
                mainDisplayKanjiElements = nonRKForms.filter(k_ele => k_ele.co);
            }
            if (mainDisplayKanjiElements.length === 0) { 
                mainDisplayKanjiElements = nonRKForms.filter(k_ele =>
                    !(k_ele.tg && k_ele.tg.includes(USUALLY_KANA_WRITING_TAG))
                );
            }
            if (mainDisplayKanjiElements.length === 0 && nonRKForms.length > 0) { 
                 mainDisplayKanjiElements = nonRKForms;
            }
        }
        let otherKanjiTextsToDisplaySeparately = nonRKForms
            .filter(k_ele => !mainDisplayKanjiElements.find(main_k => main_k.tx === k_ele.tx))
            .map(k_ele => k_ele.tx);
        otherKanjiTextsToDisplaySeparately = otherKanjiTextsToDisplaySeparately.concat(rKForms.map(k_ele => k_ele.tx));
        let mainDisplayHtml = "";
        if (displayAsUk) {
            mainDisplayHtml = `<span class="kana-char">${primaryKanaText || ''}</span>`;
            let parentheticalKanjiTexts = mainDisplayKanjiElements.map(k => k.tx);
            if (parentheticalKanjiTexts.length > 0) {
                mainDisplayHtml += ` <span class="kanji-in-parens">(${[...new Set(parentheticalKanjiTexts)].join('、')})</span>`;
            }
        } else { 
            if (mainDisplayKanjiElements.length === 0) { 
                mainDisplayHtml = `<span class="kana-char">${primaryKanaText || (entry.KJ[0]?.tx || '')}</span>`;
            } else if (mainDisplayKanjiElements.length === 1) {
                mainDisplayHtml = generateSingleFuriganaHtml(mainDisplayKanjiElements[0].tx, primaryKanaText);
            } else { 
                let furiganaParts = mainDisplayKanjiElements.map(k_ele => 
                    generateSingleFuriganaHtml(k_ele.tx, primaryKanaText)
                );
                mainDisplayHtml = [...new Set(furiganaParts)].join('、');
            }
        }
        return {
            html: mainDisplayHtml || `<span class="kana-char">${primaryKanaText || ''}</span>`,
            otherForms: [...new Set(otherKanjiTextsToDisplaySeparately)].filter(Boolean)
        };
    }
    
    function generateSingleFuriganaHtml(kanjiStringToParse, fullKanaReading) {
        let html = '';
        if (!fullKanaReading || kanjiStringToParse === fullKanaReading || wanakana.isKana(kanjiStringToParse)) {
            return `<span class="kanji-char">${kanjiStringToParse}</span>`;
        }
        const kanjiTokens = wanakana.tokenize(kanjiStringToParse, { detailed: true });
        const hiraganaReading = wanakana.toHiragana(fullKanaReading); 
        let readingIdx = 0;
        for (let i = 0; i < kanjiTokens.length; i++) {
            const token = kanjiTokens[i];
            if (token.type === 'kana' || token.type === 'hiragana' || token.type === 'katakana') {
                html += `<span class="kana-char">${token.value}</span>`;
                const lengthToAdvance = wanakana.toHiragana(token.value).length;
                readingIdx += lengthToAdvance;
            } else if (token.type === 'japanesePunctuation' && token.value === 'ー') {
                html += token.value;
                readingIdx += 1; 
            } else if (token.type === 'kanji') {
                let furigana = '';
                const currentKanjiChar = token.value;
                const remainingReading = hiraganaReading.substring(readingIdx);
                let nextKanaTokenIndex = -1;
                let nextKanjiTokenIndex = -1;
                for (let j = i + 1; j < kanjiTokens.length; j++) {
                    if (kanjiTokens[j].type === 'kana' || kanjiTokens[j].type === 'hiragana' || kanjiTokens[j].type === 'katakana') {
                        nextKanaTokenIndex = j;
                        break;
                    }
                    if (kanjiTokens[j].type === 'kanji') {
                         nextKanjiTokenIndex = j;
                         break; 
                    }
                }
                if (nextKanaTokenIndex !== -1) {
                    const nextKanaOkurigana = kanjiTokens[nextKanaTokenIndex].value;
                    const okuriganaPosInReading = remainingReading.indexOf(wanakana.toHiragana(nextKanaOkurigana));
                    if (okuriganaPosInReading !== -1) {
                        furigana = remainingReading.substring(0, okuriganaPosInReading);
                    } else {
                        furigana = remainingReading.substring(0, currentKanjiChar.length);
                    }
                } else {
                    if (nextKanjiTokenIndex === -1) {
                        furigana = remainingReading;
                    } else {
                         furigana = remainingReading.substring(0, currentKanjiChar.length);
                    }
                }
                if (wanakana.isKanji(furigana) && furigana === currentKanjiChar) { 
                    html += currentKanjiChar;
                } else {
                    html += `<ruby>${currentKanjiChar}<rt>${furigana || currentKanjiChar}</rt></ruby>`;
                }
                readingIdx += furigana.length;
            } else {
                html += token.value;
            }
        }
        if (!html.includes('<ruby>')) { 
            return `<span class="kanji-char">${kanjiStringToParse}</span>`;
        }
        return html.replace(/<rt><\/rt>/g, ''); 
    }

function getTagsForEntry(entry) {
    const tags = new Set();
    const tagDetails = [];

    if (entry.jlptLevel) {
        tagDetails.push({ text: entry.jlptLevel, class: 'tag-jlpt' });
    }

    const primaryKanaObjForTag = entry.KN.find(k => k.co) || entry.KN[0];
    const displayAsUkForTag = shouldDisplayAsUsuallyKana(entry, entry.KJ.map(k => k.tx).filter(Boolean), primaryKanaObjForTag);
    if (displayAsUkForTag && !tagDetails.some(t => t.text === 'Kana')) {
        tagDetails.push({ text: 'Kana', class: 'tag-kana-only' });
    }

    if (entry.KJ.some(k => k.co) || entry.KN.some(k => k.co)) {
        tagDetails.push({ text: 'Común', class: 'tag-common' });
    }

    // 1. Etiquetas desde SE.pS y SE.ms
    entry.SE.forEach(s => {
        s.pS?.forEach(pos => {
            const simplePos = simplifyPos(pos);
            if (simplePos && !tags.has(simplePos.text)) {
                tags.add(simplePos.text);
                tagDetails.push(simplePos);
            }
        });

        s.ms?.forEach(m => {
            const simpleMisc = simplifyMisc(m);
            if (simpleMisc && !tags.has(simpleMisc.text)) {
                tags.add(simpleMisc.text);
                tagDetails.push(simpleMisc);
            }
        });
    });

    // 2. Etiquetas embebidas en los glosses (como JSON en texto)
    function extractTagsFromGlossText(glossText) {
        const tagList = [];
        if (!glossText || typeof glossText !== 'string') return tagList;

        const parts = glossText.split('|');
        parts.forEach(part => {
            const match = part.match(/{.*}/);
            if (match) {
                try {
                    const meta = JSON.parse(match[0]);
                    meta.pS?.forEach(pos => {
                        const simplified = simplifyPos(pos);
                        if (simplified && !tags.has(simplified.text)) {
                            tags.add(simplified.text);
                            tagList.push(simplified);
                        }
                    });
                    meta.ms?.forEach(misc => {
                        const simplified = simplifyMisc(misc);
                        if (simplified && !tags.has(simplified.text)) {
                            tags.add(simplified.text);
                            tagList.push(simplified);
                        }
                    });
                } catch (e) {
                    console.warn("Error al parsear metadatos en gloss:", match[0], e);
                }
            }
        });

        return tagList;
    }

    // Aplica extractTagsFromGlossText sobre los glosses de cada SE
    entry.SE.forEach(se => {
        const glossTags1 = extractTagsFromGlossText(se.GL?.[0]?.tx || se.GL_1_tx);
        const glossTags2 = extractTagsFromGlossText(se.GL?.[1]?.tx || se.GL_2_tx);
        glossTags1.concat(glossTags2).forEach(tag => {
            if (!tags.has(tag.text)) {
                tags.add(tag.text);
                tagDetails.push(tag);
            }
        });
    });

    return tagDetails;
}


    function simplifyPos(posCode) {
        if (posCode.startsWith('n')) return { text: 'Sustantivo', class: 'tag-noun' };
        if (posCode === 'v1' || posCode === 'v1-s') return { text: 'Verbo Ichidan', class: 'tag-verb' };
        if (posCode.startsWith('v5')) return { text: 'Verbo Godan', class: 'tag-verb' };
        if (posCode === 'vs' || posCode === 'vs-s' || posCode === 'vs-i') return { text: 'Verbo Suru', class: 'tag-verb' };
        if (posCode === 'vk') return { text: 'Verbo Kuru', class: 'tag-verb' };
        if (posCode === 'adj-i') return { text: 'Adjetivo-i', class: 'tag-adjective' };
        if (posCode === 'adj-na') return { text: 'Adjetivo-na', class: 'tag-adjective' };
        if (posCode === 'adj-no') return { text: 'Sust. + No (Adj.)', class: 'tag-adjective' };
        if (posCode === 'adj-pn') return { text: 'Pre-Sust. Adjetival', class: 'tag-adjective' };
        if (posCode === 'adj-t' || posCode === 'adj-f') return { text: 'Adjetivo (Otro)', class: 'tag-adjective' };
        if (posCode === 'adv' || posCode === 'adv-to') return { text: 'Adverbio', class: 'tag-adverb' }; 
        if (posCode === 'prt') return { text: 'Partícula', class: 'tag-particle' };
        if (posCode === 'conj') return { text: 'Conjunción', class: 'tag-conjunction' };
        if (posCode === 'int') return { text: 'Interjección', class: 'tag-interjection' };
        if (posCode === 'pn') return { text: 'Pronombre', class: 'tag-pronoun' };
        if (posCode.startsWith('aux')) return { text: 'Auxiliar', class: 'tag-auxiliary' };
        if (posCode === 'ctr') return { text: 'Contador', class: 'tag-global' };
        if (posCode === 'exp') return { text: 'Expresión', class: 'tag-expression' };
        if (posCode === 'num') return { text: 'Numérico', class: 'tag-numeric' };
        if (posCode === 'unc') return { text: 'Sin clasificar', class: 'tag-global' };
        if (posCode.startsWith('pref')) return { text: 'Prefijo', class: 'tag-global' };
        if (posCode.startsWith('suf')) return { text: 'Sufijo', class: 'tag-global' };
        return null;
    }

    function simplifyMisc(miscCode) {
        if (miscCode === 'abbr') return { text: 'Abreviación', class: 'tag-global' };
        if (miscCode === 'ateji') return { text: 'Ateji', class: 'tag-global' };
        if (miscCode === 'yoji') return { text: 'Yojijukugo', class: 'tag-global' };
        if (miscCode.includes('-ben')) return { text: `Dialecto ${miscCode.replace('-ben','')}`, class: 'tag-global'};
        if (miscCode === 'chn') return { text: 'Infantil', class: 'tag-global' };
        if (miscCode === 'col') return { text: 'Coloquial', class: 'tag-global' };
        if (miscCode === 'derog') return { text: 'Derogatorio', class: 'tag-global' };
        if (miscCode === 'fam') return { text: 'Familiar', class: 'tag-global' };
        if (miscCode === 'fem') return { text: 'Femenino', class: 'tag-global' };
        if (miscCode === 'hon') return { text: 'Honorífico', class: 'tag-global' };
        if (miscCode === 'hum') return { text: 'Humilde', class: 'tag-global' };
        if (miscCode === 'id') return { text: 'Modismo', class: 'tag-global' };
        if (miscCode === 'joc') return { text: 'Jocoso', class: 'tag-global' };
        if (miscCode === 'on-mim') return { text: 'Onomatopeya', class: 'tag-global' };
        if (miscCode === 'male') return { text: 'Masculino', class: 'tag-global' };
        if (miscCode === 'obs') return { text: 'Obsoleto', class: 'tag-global' };
        if (miscCode === 'poet') return { text: 'Poético', class: 'tag-global' };
        if (miscCode === 'pol') return { text: 'Formal', class: 'tag-global' };
        if (miscCode === 'sl') return { text: 'Argot', class: 'tag-global' };
        if (miscCode === 'vulg') return { text: 'Vulgar', class: 'tag-global' };
        return null;
    }

    function initDmakDict(kanjiCharacter) {
        dmakIsAnimatingOnClickDict = false; 
        if (dmakInstanceDict) {
            if (dmakInstanceDict.timeouts) {
                for (const type in dmakInstanceDict.timeouts) {
                    if (Array.isArray(dmakInstanceDict.timeouts[type])) {
                        dmakInstanceDict.timeouts[type].forEach(clearTimeout);
                    }
                    dmakInstanceDict.timeouts[type] = [];
                }
            }
            dmakInstanceDict.erase();
        }
        kanjiDetailAnimationTarget.innerHTML = ''; 
        const strokeColor = userProgress.theme === 'dark' ? '#FFFFFF' : '#333333'; 
        let dmakIsReady = false;
        let dmakAttempted = false;
        try {
            dmakInstanceDict = new Dmak(kanjiCharacter, {
                uri: '../data/kanjivg/kanji/', 
                skipLoad: false, autoplay: false, step: 0.015, 
                height: 200, width: 200,
                element: "kanji-detail-animation-target-dict",
                stroke: { animated: { drawing: false, erasing: false }, order: { visible: false }, attr: { "stroke": strokeColor, "stroke-width": 4 } },
                grid: { show: false },
                loaded: function(strokes) {
                    dmakAttempted = true;
                    if (strokes && strokes.length > 0) dmakIsReady = true;
                },
                drew: function(index) { 
                    if (!dmakInstanceDict || !dmakInstanceDict.strokes) return;
                    if (dmakIsAnimatingOnClickDict && index === (dmakInstanceDict.strokes.length - 1)) {
                        dmakIsAnimatingOnClickDict = false;
                        setTimeout(() => { 
                            const paths = kanjiDetailAnimationTarget.querySelectorAll('svg path');
                            paths.forEach(p => p.setAttribute('stroke', strokeColor));
                        }, 50);
                    }
                }
            });
        } catch (error) {
            dmakAttempted = true; dmakIsReady = false;
            console.error(`Error Dmak para ${kanjiCharacter}:`, error);
        }
        setTimeout(() => {
            if (dmakInstanceDict && dmakInstanceDict.text === kanjiCharacter && dmakIsReady && !dmakIsAnimatingOnClickDict) {
                dmakInstanceDict.render(); 
                const paths = kanjiDetailAnimationTarget.querySelectorAll('svg path');
                paths.forEach(p => p.setAttribute('stroke', strokeColor));
                kanjiDetailAnimationTarget.onclick = () => {
                    if (dmakInstanceDict && dmakIsReady) {
                        if (dmakIsAnimatingOnClickDict) { 
                            if (dmakInstanceDict.timeouts && dmakInstanceDict.timeouts.play) {
                                dmakInstanceDict.timeouts.play.forEach(clearTimeout);
                                dmakInstanceDict.timeouts.play = [];
                            }
                        }
                        dmakIsAnimatingOnClickDict = true;
                        dmakInstanceDict.erase();
                        dmakInstanceDict.options.stroke.animated.drawing = true; 
                        dmakInstanceDict.render(); 
                    }
                };
            } else if (dmakAttempted && !dmakIsReady) {
                kanjiDetailAnimationTarget.innerHTML = `<div class="kanji-animation-fallback-text-dict kanji-char">${kanjiCharacter}</div>`;
                kanjiDetailAnimationTarget.onclick = null;
            }
            else if (!dmakInstanceDict && dmakAttempted) {
                 kanjiDetailAnimationTarget.innerHTML = `<div class="kanji-animation-fallback-text-dict kanji-char">${kanjiCharacter}</div>`;
                 kanjiDetailAnimationTarget.onclick = null;
            }
        }, 150); 
    }


    // ========== MANEJO DE COLECCIONES (Modal de Añadir a Colección/Favoritos) ==========
    // function updateCollectionsCountSidebar() { // REMOVED }
    
    // function renderCollectionsView(activeTabType = 'vocab') { // REMOVED }
    
    function openModalForCollection(type, itemId, itemName) {
        if (!itemId || !itemName) {
            addToCollectionFeedback.textContent = "Error: Información del item incompleta.";
            return;
        }
        currentItemForModal = { type: type, id: itemId, name: itemName };
        currentCollectionTypeForModal = type; 
        modalCollectionTitle.textContent = `Añadir ${type === 'vocab' ? 'Palabra' : 'Kanji'} a Colección`;
        populateExistingCollectionsInModalDict();
        newCollectionNameModalInput.value = '';
        addToCollectionFeedback.textContent = '';
        addToCollectionModal.classList.remove('hidden');
    }

    function populateExistingCollectionsInModalDict() {
        existingCollectionsListModal.innerHTML = '';
        const collections = userProgress.dictionaryCollections[currentCollectionTypeForModal] || [];
        collections.forEach(collection => {
            const div = document.createElement('div');
            div.className = 'collection-radio-item';
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `dict-coll-modal-chk-${collection.name.replace(/\s+/g, '-')}-${currentCollectionTypeForModal}`;
            checkbox.value = collection.name;
            checkbox.checked = currentItemForModal && collection.items.includes(currentItemForModal.id);
            const label = document.createElement('label');
            label.htmlFor = checkbox.id;
            label.textContent = `${collection.name} (${collection.items.length})`;
            checkbox.addEventListener('change', (e) => {
                toggleItemInExistingCollectionDict(collection.name, e.target.checked);
            });
            div.appendChild(checkbox);
            div.appendChild(label);
            existingCollectionsListModal.appendChild(div);
        });
    }

    function toggleItemInExistingCollectionDict(collectionName, isChecked) {
        const collection = userProgress.dictionaryCollections[currentCollectionTypeForModal].find(c => c.name === collectionName);
        if (collection && currentItemForModal) {
            const itemExists = collection.items.includes(currentItemForModal.id);
            let feedbackMessage = '';
            if (isChecked && !itemExists) {
                collection.items.push(currentItemForModal.id);
                feedbackMessage = `'${currentItemForModal.name}' añadido a "${collectionName}".`;
            } else if (!isChecked && itemExists) {
                collection.items = collection.items.filter(id => id !== currentItemForModal.id);
                feedbackMessage = `'${currentItemForModal.name}' quitado de "${collectionName}".`;
            }
            if (feedbackMessage) {
                saveUserProgressLocal();
                addToCollectionFeedback.textContent = feedbackMessage;
                const label = existingCollectionsListModal.querySelector(`label[for="dict-coll-modal-chk-${collection.name.replace(/\s+/g, '-')}-${currentCollectionTypeForModal}"]`);
                if (label) label.textContent = `${collection.name} (${collection.items.length})`;
                // if (collectionsView.classList.contains('active-dict-view')) { // REMOVED
                //     renderCollectionsView(currentCollectionTypeForModal);
                // }
                if (collection.isSystem) { 
                    if (currentCollectionTypeForModal === 'vocab' && currentDetailTerm && currentDetailTerm.id === currentItemForModal.id) {
                        updateVocabLikeButtonVisual();
                    } else if (currentCollectionTypeForModal === 'kanji' && currentDetailKanji && currentDetailKanji.l === currentItemForModal.id) { 
                        updateKanjiLikeButtonVisual();
                    }
                }
            }
        }
    }
    
    function handleCreateAndAddToNewCollectionModalDict() {
        const newName = newCollectionNameModalInput.value.trim();
        if (!newName) {
            addToCollectionFeedback.textContent = "El nombre no puede estar vacío."; return;
        }
        if (userProgress.dictionaryCollections[currentCollectionTypeForModal].find(c => c.name.toLowerCase() === newName.toLowerCase())) {
            addToCollectionFeedback.textContent = "Ya existe una colección con este nombre."; return;
        }
        if (currentItemForModal) {
            userProgress.dictionaryCollections[currentCollectionTypeForModal].push({ name: newName, items: [currentItemForModal.id], isSystem: false });
            saveUserProgressLocal();
            addToCollectionFeedback.textContent = `'${currentItemForModal.name}' añadido a la nueva colección "${newName}".`;
            newCollectionNameModalInput.value = '';
            populateExistingCollectionsInModalDict();
            // if (collectionsView.classList.contains('active-dict-view')) renderCollectionsView(currentCollectionTypeForModal); // REMOVED
        }
    }
    
    function updateVocabLikeButtonVisual() {
        const favCollection = userProgress.dictionaryCollections.vocab.find(c => c.isSystem && c.name === "Favoritos Vocab");
        if (vocabDetailLikeButton && currentDetailTerm && favCollection) {
            vocabDetailLikeButton.classList.toggle('liked', favCollection.items.includes(currentDetailTerm.id));
        }
    }
    function updateKanjiLikeButtonVisual() {
        const favCollection = userProgress.dictionaryCollections.kanji.find(c => c.isSystem && c.name === "Favoritos Kanji (Diccionario)");
         if (kanjiDetailLikeButtonDict && currentDetailKanji && favCollection) {
            kanjiDetailLikeButtonDict.classList.toggle('liked', favCollection.items.includes(currentDetailKanji.l)); 
        }
    }


    // ========== INICIALIZACIÓN Y EVENT LISTENERS GLOBALES ==========
    function initComponents() {
        if (searchButton) searchButton.addEventListener('click', () => performSearch());
        if (searchInput) searchInput.addEventListener('keypress', (e) => { if (e.key === 'Enter') performSearch(); });

        if (vocabShowMoreButton) vocabShowMoreButton.addEventListener('click', renderPaginatedVocabResults);
        if (kanjiShowMoreButton) kanjiShowMoreButton.addEventListener('click', renderPaginatedKanjiResults);
        if (vocabExamplesShowMoreButton) vocabExamplesShowMoreButton.addEventListener('click', renderPaginatedExamples);
        if (kanjiCommonWordsShowMoreButton) kanjiCommonWordsShowMoreButton.addEventListener('click', () => renderPaginatedKanjiRelatedWords('common'));
        if (kanjiAllWordsShowMoreButton) kanjiAllWordsShowMoreButton.addEventListener('click', () => renderPaginatedKanjiRelatedWords('all'));

        if (searchResultsTabButtons) {
            searchResultsTabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    searchResultsTabButtons.forEach(btn => btn.classList.remove('active-tab'));
                    button.classList.add('active-tab');
                    const targetSectionId = button.dataset.target;
                    document.querySelectorAll('.results-section').forEach(section => {
                        section.classList.toggle('active-results-section-mobile', section.id === targetSectionId);
                    });
                });
            });
        }
        window.addEventListener('resize', handleResponsiveTabs);


        if (vocabDetailBackButton) vocabDetailBackButton.addEventListener('click', goBack);
        if (kanjiDetailBackButton) kanjiDetailBackButton.addEventListener('click', goBack);
        // if (collectionsBackToMainButton) collectionsBackToMainButton.addEventListener('click', () => { navigationHistory = []; showView('main'); }); // REMOVED
        
        if (kanjiEditNotesButton) kanjiEditNotesButton.addEventListener('click', () => {
            kanjiUserNotesTextarea.readOnly = false; kanjiUserNotesTextarea.focus();
            kanjiSaveNotesButton.classList.remove('hidden'); kanjiEditNotesButton.classList.add('hidden');
        });
        if (kanjiSaveNotesButton) kanjiSaveNotesButton.addEventListener('click', () => {
            if (currentDetailKanji) {
                userProgress.kanjiUserNotes[currentDetailKanji.l] = kanjiUserNotesTextarea.value; 
                saveUserProgressLocal();
                kanjiUserNotesTextarea.readOnly = true;
                kanjiSaveNotesButton.classList.add('hidden'); kanjiEditNotesButton.classList.remove('hidden');
                kanjiResetNotesButton.disabled = !kanjiUserNotesTextarea.value;
            }
        });
       if (kanjiResetNotesButton) kanjiResetNotesButton.addEventListener('click', () => {
            if (currentDetailKanji && confirm("¿Seguro que quieres borrar tu nota personal para este kanji?")) {
                kanjiUserNotesTextarea.value = ''; delete userProgress.kanjiUserNotes[currentDetailKanji.l]; 
                saveUserProgressLocal();
                kanjiUserNotesTextarea.readOnly = true;
                kanjiSaveNotesButton.classList.add('hidden'); kanjiEditNotesButton.classList.remove('hidden');
                kanjiResetNotesButton.disabled = true;
            }
        });

        if (vocabDetailCopyButton) vocabDetailCopyButton.addEventListener('click', () => {
            if (currentDetailTerm) navigator.clipboard.writeText(currentDetailTerm.KJ[0]?.tx || currentDetailTerm.KN[0]?.tx || '');
        });
        if (vocabDetailLikeButton) vocabDetailLikeButton.addEventListener('click', () => {
            const favCollection = userProgress.dictionaryCollections.vocab.find(c => c.isSystem && c.name === "Favoritos Vocab");
            if (currentDetailTerm && favCollection) {
                currentItemForModal = { type: 'vocab', id: currentDetailTerm.id, name: (currentDetailTerm.KJ[0]?.tx || currentDetailTerm.KN[0]?.tx) };
                currentCollectionTypeForModal = 'vocab'; 
                toggleItemInExistingCollectionDict(favCollection.name, !favCollection.items.includes(currentDetailTerm.id));
            }
        });
        if (vocabDetailAddCollectionButton) vocabDetailAddCollectionButton.addEventListener('click', () => {
            if (currentDetailTerm) openModalForCollection('vocab', currentDetailTerm.id, currentDetailTerm.KJ[0]?.tx || currentDetailTerm.KN[0]?.tx);
        });

        if (kanjiDetailCopyButtonDict) kanjiDetailCopyButtonDict.addEventListener('click', () => {
            if (currentDetailKanji) navigator.clipboard.writeText(currentDetailKanji.l || ''); 
        });
        if (kanjiDetailLikeButtonDict) kanjiDetailLikeButtonDict.addEventListener('click', () => {
            const favCollection = userProgress.dictionaryCollections.kanji.find(c => c.isSystem && c.name === "Favoritos Kanji (Diccionario)");
            if (currentDetailKanji && favCollection) {
                currentItemForModal = { type: 'kanji', id: currentDetailKanji.l, name: currentDetailKanji.l }; 
                currentCollectionTypeForModal = 'kanji'; 
                toggleItemInExistingCollectionDict(favCollection.name, !favCollection.items.includes(currentDetailKanji.l)); 
            }
        });
        if (kanjiDetailAddCollectionButtonDict) kanjiDetailAddCollectionButtonDict.addEventListener('click', () => {
            if (currentDetailKanji) openModalForCollection('kanji', currentDetailKanji.l, currentDetailKanji.l); 
        });

        // if (dictionaryCollectionsButton) dictionaryCollectionsButton.addEventListener('click', () => showView('collections')); // REMOVED
        // if (collectionTabButtons) collectionTabButtons.forEach(btn => { // REMOVED
        //     btn.addEventListener('click', () => {
        //         collectionTabButtons.forEach(b => b.classList.remove('active'));
        //         btn.classList.add('active');
        //         renderCollectionsView(btn.dataset.type);
        //     });
        // });
        
        if (addToCollectionModalClose) addToCollectionModalClose.addEventListener('click', () => addToCollectionModal.classList.add('hidden'));
        if (createAndAddToNewCollectionBtnModal) createAndAddToNewCollectionBtnModal.addEventListener('click', handleCreateAndAddToNewCollectionModalDict);
        // if (createCollectionModalClose) createCollectionModalClose.addEventListener('click', () => createCollectionModal.classList.add('hidden')); // REMOVED
        
        // Event listener for confirmCreateCollectionBtnModal REMOVED
        // Event listener for createNewCollectionButtonDict REMOVED


        window.addEventListener('click', (event) => { 
            if (event.target === addToCollectionModal) addToCollectionModal.classList.add('hidden');
            // if (event.target === createCollectionModal) createCollectionModal.classList.add('hidden'); // REMOVED
        });

        if (searchOriginalQueryLink) searchOriginalQueryLink.addEventListener('click', (e) => {
            e.preventDefault();
            const originalTermToSearch = searchOriginalQueryLink.textContent;
            if (originalTermToSearch) {
                searchInput.value = originalTermToSearch;
                performSearch(true); 
            }
        });
    }

    // --- Arranque ---
    await loadAllDictionaryData();
    
    const navigatedByHashOnLoad = handleDictionaryNavigation();
    if (!navigatedByHashOnLoad && !document.querySelector('.active-dict-view') && mainView) {
        showView('main');
    }

    window.addEventListener('hashchange', handleDictionaryNavigation);

    handleResponsiveTabs(); 

});
